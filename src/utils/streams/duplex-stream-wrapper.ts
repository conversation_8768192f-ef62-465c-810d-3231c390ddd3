import type { ClientDuplexStream } from '@grpc/grpc-js'
import { type Awaitable, createDeferred, withTimeout } from '@kdt310722/utils/promise'
import { StreamWrapper, type StreamWrapperOptions, type StreamWrapperTimeoutOptions } from './stream-wrapper'

export interface DuplexStreamWrapperTimeoutOptions extends StreamWrapperTimeoutOptions {
    end?: number
}

export interface DuplexStreamWrapperOptions extends Omit<StreamWrapperOptions, 'timeout'> {
    timeout?: DuplexStreamWrapperTimeoutOptions
}

export class DuplexStreamWrapper<TRequest, TResponse, TStream extends ClientDuplexStream<TRequest, TResponse> = ClientDuplexStream<TRequest, TResponse>> extends StreamWrapper<TResponse, TStream> {
    protected readonly endTimeout: number

    public constructor(protected override readonly subscriber: () => Awaitable<TStream>, options: DuplexStreamWrapperOptions = {}) {
        super(subscriber, options)

        this.endTimeout = options.timeout?.end ?? 10_000
    }

    public async write(data: TRequest) {}

    protected override async closeStream(stream: TStream) {
        return this.end(stream).then(() => super.closeStream(stream))
    }

    protected async end(stream: TStream) {
        if (!this.isWritable(stream)) {
            return
        }

        const promise = createDeferred<void>()
        const endHandler = () => promise.resolve()

        stream.once('end', endHandler)
        stream.end()

        await withTimeout(promise, this.endTimeout).catch(() => null).finally(() => {
            stream.removeListener('end', endHandler)
        })
    }

    protected isWritable(stream: TStream) {
        return !stream.writableEnded && stream.writable
    }
}
